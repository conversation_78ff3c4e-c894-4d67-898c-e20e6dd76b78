import 'dart:async';
import 'package:rooo_driver/global/export/app_export.dart';
import 'package:rooo_driver/network/RestApis.dart';

class LocationBasedHeartbeat {
  static Timer? _heartbeatTimer;
  static DateTime? _lastHeartbeatTime;
  static const Duration HEARTBEAT_INTERVAL = Duration(minutes: 20);
  
  /// Start the location-based heartbeat system
  static void startHeartbeat() {
    print("Starting location-based heartbeat system");
    
    // Cancel any existing timer
    _heartbeatTimer?.cancel();
    
    // Start periodic heartbeat
    _heartbeatTimer = Timer.periodic(HEARTBEAT_INTERVAL, (timer) async {
      await _performHeartbeat();
    });
    
    // Perform initial heartbeat
    _performHeartbeat();
  }
  
  /// Stop the heartbeat system
  static void stopHeartbeat() {
    print("Stopping location-based heartbeat system");
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }
  
  /// Perform heartbeat API call
  static Future<void> _performHeartbeat() async {
    try {
      // Check if driver is online
      if (!GlobalState.isDriverOnline) {
        print("Driver is offline, skipping heartbeat");
        return;
      }
      
      // Get driver credentials
      String token = sharedPref.getString(TOKEN) ?? "";
      String driverId = (sharedPref.getInt(USER_ID) ?? "").toString();
      
      if (token.isEmpty || driverId.isEmpty) {
        print("Missing credentials for heartbeat");
        return;
      }
      
      print("Performing heartbeat API call");
      await updateDriverOnlineStatus(driverId, token);
      
      _lastHeartbeatTime = DateTime.now();
      print("Heartbeat completed successfully at ${_lastHeartbeatTime}");
      
    } catch (e) {
      print("Heartbeat error: $e");
    }
  }
  
  /// Trigger heartbeat on location update (for background reliability)
  static void onLocationUpdate() {
    // Only trigger if enough time has passed since last heartbeat
    if (_lastHeartbeatTime == null || 
        DateTime.now().difference(_lastHeartbeatTime!).inMinutes >= 20) {
      _performHeartbeat();
    }
  }
  
  /// Get time since last heartbeat
  static Duration? getTimeSinceLastHeartbeat() {
    if (_lastHeartbeatTime == null) return null;
    return DateTime.now().difference(_lastHeartbeatTime!);
  }
  
  /// Check if heartbeat is active
  static bool get isActive => _heartbeatTimer?.isActive ?? false;
}
